---
---

<div
  class="max-w-md w-full bg-white/90 dark:bg-gray-900/90 backdrop-blur-sm rounded-2xl shadow-xl p-8 space-y-8 transform transition-all duration-300 border border-gray-200/50 dark:border-gray-700/50"
>
  <div class="space-y-4 text-center">
    <img
      src="/logo.svg"
      alt="PraxJobs Logo"
      class="h-16 mx-auto mb-4 transform hover:scale-105 transition-transform duration-300 filter dark:invert"
    />
    <h1
      class="text-4xl font-bold bg-gradient-to-r from-gray-900 via-gray-700 to-gray-900 dark:from-white dark:via-gray-200 dark:to-white bg-clip-text text-transparent animate-gradient-x"
    >
      Create Account
    </h1>
    <p class="text-gray-600 dark:text-gray-300">Start your career journey</p>
  </div>

  <form id="signupForm" class="space-y-4">
    <div class="space-y-2">
      <input
        id="email"
        name="email"
        type="email"
        placeholder="Enter your email"
        class="w-full px-4 py-3 border-2 rounded-xl dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100 focus:border-primary dark:focus:border-primary outline-none transition-colors duration-200"
        required
      />
      <div id="emailError" class="text-red-500 text-xs hidden"></div>
    </div>
    <div class="space-y-2">
      <input
        id="password"
        name="password"
        type="password"
        placeholder="Enter your password"
        class="w-full px-4 py-3 border-2 rounded-xl dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100 focus:border-primary dark:focus:border-primary outline-none transition-colors duration-200"
        required
        minlength="8"
        autocomplete="new-password"
      />
      <div id="passwordError" class="text-red-500 text-xs hidden"></div>
      <!-- Updated text to reflect 8 characters -->
      <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
        Password must be at least 8 characters long
      </div>
    </div>
    <div class="space-y-2">
      <input
        id="confirmPassword"
        name="confirmPassword"
        type="password"
        placeholder="Confirm your password"
        class="w-full px-4 py-3 border-2 rounded-xl dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100 focus:border-primary dark:focus:border-primary outline-none transition-colors duration-200"
        required
        autocomplete="new-password"
      />
      <div id="confirmPasswordError" class="text-red-500 text-xs hidden"></div>
    </div>
    <div
      id="signupErrorContainer"
      class="text-red-500 text-sm hidden rounded-md p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800/30"
    >
    </div>
    <button
      type="submit"
      class="w-full bg-gray-800 dark:bg-gray-100 dark:hover:bg-white text-white dark:text-gray-900 py-3 rounded-xl hover:bg-black"
    >
      Create Account
    </button>
  </form>

  <!-- Divider -->
  <div class="relative">
    <div class="absolute inset-0 flex items-center">
      <div class="w-full border-t border-gray-200 dark:border-gray-700"></div>
    </div>
    <div class="relative flex justify-center text-sm">
      <span
        class="px-2 bg-white/90 dark:bg-gray-900/90 text-gray-500 dark:text-gray-400"
      >
        Or continue with
      </span>
    </div>
  </div>

  <!-- Google Sign Up Button -->
  <button
    id="googleSignIn"
    type="button"
    class="w-full flex items-center justify-center gap-3 py-3 px-4
               border-2 border-gray-200 dark:border-gray-700 rounded-xl
               hover:bg-gray-50 dark:hover:bg-gray-800
               transition-colors duration-200
               text-gray-700 dark:text-gray-200"
  >
    <img src="/google.svg" alt="Google" class="w-5 h-5" />
    <span>Sign up with Google</span>
  </button>

  <div class="text-center text-sm">
    <span class="text-gray-600 dark:text-gray-400"
      >Already have an account?</span
    >
    <a
      href="/login"
      class="text-primary text-gray-950 dark:text-white hover:underline ml-1"
      >Sign in</a
    >
  </div>
</div>

<script>
  import { authService } from "../../lib/auth";

  // Email/Password Sign Up
  const signupForm = document.getElementById("signupForm");
  const errorContainer = document.getElementById("signupErrorContainer");

  // Client-side validation functions
  function validateEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Updated to reflect 8 characters
  function validatePassword(password: string): boolean {
    return password.length >= 8;
  }

  // Add input validation listeners
  const emailInput = document.getElementById("email") as HTMLInputElement;
  const passwordInput = document.getElementById("password") as HTMLInputElement;
  const confirmPasswordInput = document.getElementById(
    "confirmPassword"
  ) as HTMLInputElement;
  const emailError = document.getElementById("emailError");
  const passwordError = document.getElementById("passwordError");
  const confirmPasswordError = document.getElementById("confirmPasswordError");

  emailInput?.addEventListener("blur", () => {
    if (emailInput.value && !validateEmail(emailInput.value)) {
      if (emailError) {
        emailError.textContent = "Please enter a valid email address";
        emailError.classList.remove("hidden");
      }
    } else if (emailError) {
      emailError.classList.add("hidden");
    }
  });

  passwordInput?.addEventListener("blur", () => {
    if (passwordInput.value && !validatePassword(passwordInput.value)) {
      if (passwordError) {
        // Updated error message
        passwordError.textContent =
          "Password must be at least 8 characters long";
        passwordError.classList.remove("hidden");
      }
    } else if (passwordError) {
      passwordError.classList.add("hidden");
    }
  });

  confirmPasswordInput?.addEventListener("blur", () => {
    if (
      confirmPasswordInput.value &&
      passwordInput.value !== confirmPasswordInput.value
    ) {
      if (confirmPasswordError) {
        confirmPasswordError.textContent = "Passwords do not match";
        confirmPasswordError.classList.remove("hidden");
      }
    } else if (confirmPasswordError) {
      confirmPasswordError.classList.add("hidden");
    }
  });

  signupForm?.addEventListener("submit", async (e) => {
    e.preventDefault();

    // Reset all error messages
    [emailError, passwordError, confirmPasswordError].forEach((el) =>
      el?.classList.add("hidden")
    );
    if (errorContainer) {
      errorContainer.textContent = "";
      errorContainer.classList.add("hidden");
    }

    // Get form elements
    if (!emailInput || !passwordInput || !confirmPasswordInput) {
      // Removed console.error('Form inputs not found');
      // Optionally display an error in the UI if this unlikely case occurs
      if (errorContainer) {
        errorContainer.textContent = "Internal error: Form fields missing.";
        errorContainer.classList.remove("hidden");
      }
      return;
    }

    // Validate email
    if (!validateEmail(emailInput.value)) {
      if (emailError) {
        emailError.textContent = "Please enter a valid email address";
        emailError.classList.remove("hidden");
      }
      return;
    }

    // Validate password
    if (!validatePassword(passwordInput.value)) {
      if (passwordError) {
        // Updated error message
        passwordError.textContent =
          "Password must be at least 8 characters long";
        passwordError.classList.remove("hidden");
      }
      return;
    }

    // Validate passwords match
    if (passwordInput.value !== confirmPasswordInput.value) {
      if (confirmPasswordError) {
        confirmPasswordError.textContent = "Passwords do not match";
        confirmPasswordError.classList.remove("hidden");
      }
      return;
    }

    try {
      // Show loading state
      const submitButton = signupForm.querySelector('button[type="submit"]');
      if (submitButton) {
        submitButton.innerHTML =
          '<span class="inline-block animate-spin mr-2">↻</span> Creating account...';
        (submitButton as HTMLButtonElement).disabled = true;
      }

      // Clear any previous errors
      if (errorContainer) {
        errorContainer.textContent = "";
        errorContainer.classList.add("hidden");
      }

      // Disable all inputs during submission
      [emailInput, passwordInput, confirmPasswordInput].forEach((input) => {
        if (input) input.disabled = true; // Check if input exists
      });

      // Add a small delay to ensure Firebase is fully initialized (if needed, otherwise remove)
      // await new Promise(resolve => setTimeout(resolve, 500)); // Consider removing if not strictly necessary

      const user = await authService.signUpWithEmailAndPassword(
        emailInput.value,
        passwordInput.value
      );

      if (user) {
        // Show success message before redirecting
        if (errorContainer) {
          errorContainer.textContent =
            "Account created successfully! Redirecting...";
          errorContainer.classList.remove("hidden");
          errorContainer.classList.remove(
            "text-red-500",
            "bg-red-50",
            "border-red-200",
            "dark:bg-red-900/20",
            "dark:border-red-800/30"
          );
          errorContainer.classList.add(
            "text-green-600",
            "dark:text-green-400",
            "bg-green-50",
            "dark:bg-green-900/20",
            "border",
            "border-green-200",
            "dark:border-green-700/30"
          ); // Updated success styles
        }

        // Short delay before redirect for better UX
        setTimeout(() => {
          window.location.href = "/dashboard";
        }, 1000);
      }
    } catch (error) {
      // Removed console.error('Signup error:', error);

      // Show error message with better formatting
      if (errorContainer) {
        let errorMessage = "Sign up failed. Please try again.";

        if (error instanceof Error) {
          // Handle specific Firebase error messages
          if (error.message.includes("email-already-in-use")) {
            errorMessage =
              "This email is already in use. Please try another email or sign in.";
          } else if (error.message.includes("network")) {
            errorMessage =
              "Network error. Please check your internet connection and try again.";
          } else if (error.message.includes("invalid-email")) {
            errorMessage =
              "Invalid email format. Please enter a valid email address.";
          } else if (error.message.includes("weak-password")) {
            // Update weak password message to match 8 char requirement
            errorMessage =
              "Password is too weak. Please use a stronger password (at least 8 characters).";
          } else if (error.message.includes("cannot be reused")) {
            errorMessage =
              "This email was recently used for an account that was deleted. Please wait or use another email.";
          } else {
            // Fallback to the generic message or the raw error (use with caution in prod)
            errorMessage = "An unexpected error occurred. Please try again.";
            // errorMessage = error.message; // Avoid exposing raw error messages in production
          }
        }

        errorContainer.textContent = errorMessage;
        errorContainer.classList.remove("hidden");
        // Ensure error styles are applied (might have been success style before)
        errorContainer.classList.remove(
          "text-green-600",
          "dark:text-green-400",
          "bg-green-50",
          "dark:bg-green-900/20",
          "border-green-200",
          "dark:border-green-700/30"
        );
        errorContainer.classList.add(
          "text-red-500",
          "bg-red-50",
          "dark:bg-red-900/20",
          "border-red-200",
          "dark:border-red-800/30"
        );
      }

      // Reset form state
      const submitButton = signupForm.querySelector('button[type="submit"]');
      if (submitButton) {
        submitButton.innerHTML = "Create Account";
        (submitButton as HTMLButtonElement).disabled = false;
      }

      // Re-enable inputs
      [emailInput, passwordInput, confirmPasswordInput].forEach((input) => {
        if (input) input.disabled = false; // Check if input exists
      });
    }
  });

  // Google Sign In
  const googleSignInButton = document.getElementById("googleSignIn");
  googleSignInButton?.addEventListener("click", async () => {
    try {
      // Show loading state
      if (googleSignInButton) {
        googleSignInButton.innerHTML =
          '<span class="animate-spin inline-block mr-2">↻</span> Signing up...'; // Improved spinner display
        (googleSignInButton as HTMLButtonElement).disabled = true;
      }

      // Clear any previous errors
      if (errorContainer) {
        errorContainer.textContent = "";
        errorContainer.classList.add("hidden");
      }

      // signInWithGoogle now uses redirect, so it won't return here
      // The redirect will happen automatically
      await authService.signInWithGoogle();
    } catch (error) {
      // Show error message
      if (errorContainer) {
        let errorMessage = "Google Sign In failed. Please try again.";
        if (error instanceof Error) {
          errorMessage = error.message;
        }
        errorContainer.textContent = errorMessage;
        errorContainer.classList.remove("hidden");
        // Ensure error styles
        errorContainer.classList.remove(
          "text-green-600",
          "dark:text-green-400",
          "bg-green-50",
          "dark:bg-green-900/20",
          "border-green-200",
          "dark:border-green-700/30"
        );
        errorContainer.classList.add(
          "text-red-500",
          "bg-red-50",
          "dark:bg-red-900/20",
          "border-red-200",
          "dark:border-red-800/30"
        );
      }

      // Reset button state
      if (googleSignInButton) {
        googleSignInButton.innerHTML =
          '<img src="/google.svg" alt="Google" class="w-5 h-5" /><span>Sign up with Google</span>';
        (googleSignInButton as HTMLButtonElement).disabled = false;
      }
    }
  });
</script>
