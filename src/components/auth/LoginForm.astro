---
// Safely get and validate the redirect URL
const unsafeRedirect = Astro.url.searchParams.get("redirect");
let redirectUrl = "/dashboard"; // Default safe redirect
if (unsafeRedirect && unsafeRedirect.startsWith("/") && !unsafeRedirect.startsWith("//")) {
  redirectUrl = unsafeRedirect; // It's a safe, relative path
}
---

<div
  id="login-container"
  class="relative w-full"
  data-redirect-url={redirectUrl}
>
  <div
    class="max-w-md w-full bg-white/90 dark:bg-gray-900/30 backdrop-blur-sm rounded-2xl shadow-xl p-8 space-y-8 transform transition-all duration-300 border border-gray-200/50 dark:border-gray-700/50 mx-auto"
  >
    <div class="space-y-4 text-center">
      <img
        src="/logo.svg"
        alt="PraxJobs Logo"
        class="h-16 mx-auto mb-4 transform hover:scale-105 transition-transform duration-300 filter dark:invert"
      />
      <h1
        class="text-4xl font-bold bg-gradient-to-r from-gray-900 via-gray-700 to-gray-900 dark:from-white dark:via-gray-200 dark:to-white bg-clip-text text-transparent animate-gradient-x"
      >
        Sign In
      </h1>
      <p class="text-gray-600 dark:text-gray-300">Enter your email to get a secure link.</p>
    </div>

    <form id="loginForm" class="space-y-4">
      <div class="space-y-2">
        <input
          id="email"
          name="email"
          type="email"
          placeholder="Enter your email"
          class="w-full px-4 py-3 border-2 rounded-xl dark:border-gray-700 dark:bg-gray-800 dark:text-gray-100 focus:border-primary dark:focus:border-primary outline-none transition-colors duration-200"
          required
        />
      </div>
      <div id="loginErrorContainer" class="text-red-500 text-sm hidden"></div>
      <button
        type="submit"
        class="w-full bg-gray-800 dark:bg-gray-100 dark:hover:bg-white text-white dark:text-gray-900 py-3 rounded-xl hover:bg-black"
      >
        Send Login Link
      </button>
    </form>

    <!-- Divider -->
    <div class="relative">
      <div class="absolute inset-0 flex items-center">
        <div class="w-full border-t border-gray-200 dark:border-gray-700"></div>
      </div>
      <div class="relative flex justify-center text-sm">
        <span
          class="p-2 rounded-full bg-white dark:bg-gray-900 text-gray-500 dark:text-gray-400"
        >
          Or continue with
        </span>
      </div>
    </div>

    <!-- Google Sign In Button -->
    <button
      id="googleSignIn"
      type="button"
      class="w-full flex items-center justify-center gap-3 py-3 px-4
                   border-2 border-gray-200 dark:border-gray-700 rounded-xl
                   hover:bg-gray-50 dark:hover:bg-gray-800
                   transition-colors duration-200
                   text-gray-700 dark:text-gray-200"
    >
      <img src="/google.svg" alt="Google" class="w-5 h-5" />
      <span>Sign in with Google</span>
    </button>

    <!-- Sign Up Link -->
    <div class="text-center text-sm">
      <span class="text-gray-600 dark:text-gray-400"
        >Don't have an account?</span
      >
      <a
        href="/signup"
        class="text-primary text-gray-950 dark:text-white hover:underline ml-1"
        >Sign up</a
      >
    </div>
  </div>
</div>

<script>
  import { authService } from "../../lib/auth";

  // Note: redirectUrl is handled in the auth service's handleRedirectResult method

  // Email/Password Login
  const loginForm = document.getElementById("loginForm");
  const errorContainer = document.getElementById("loginErrorContainer");

  loginForm?.addEventListener("submit", async (e) => {
    e.preventDefault();

    // Get form elements
    const emailInput = document.getElementById("email") as HTMLInputElement;

    if (!emailInput) {
      return; // Exit if inputs aren't found
    }

    try {
      // Show loading state
      const submitButton = loginForm.querySelector('button[type="submit"]');
      if (submitButton) {
        submitButton.innerHTML = "Sending link...";
        (submitButton as HTMLButtonElement).disabled = true;
      }

      // Clear any previous errors
      if (errorContainer) {
        errorContainer.textContent = "";
        errorContainer.classList.add("hidden");
      }

      await authService.sendSignInLinkToEmail(emailInput.value);

      // Redirect to a page informing the user to check their email
      window.location.href = "/check-email";

    } catch (error) {
      // Show error message in the UI
      if (errorContainer) {
        errorContainer.textContent =
          error instanceof Error
            ? error.message
            : "Failed to send link. Please try again.";
        errorContainer.classList.remove("hidden");
      }

      // Reset button state
      const submitButton = loginForm.querySelector('button[type="submit"]');
      if (submitButton) {
        submitButton.innerHTML = "Continue with Email";
        (submitButton as HTMLButtonElement).disabled = false;
      }
    }
  });

  // Google Sign In
  const googleSignInButton = document.getElementById("googleSignIn");
  googleSignInButton?.addEventListener("click", async () => {
    try {
      // Show loading state
      if (googleSignInButton) {
        googleSignInButton.innerHTML =
          '<span class="animate-spin inline-block mr-2">↻</span> Signing in...'; // Added inline-block and margin for spinner
        (googleSignInButton as HTMLButtonElement).disabled = true;
      }

      // Clear any previous errors
      if (errorContainer) {
        errorContainer.textContent = "";
        errorContainer.classList.add("hidden");
      }

      // signInWithGoogle now uses redirect, so it won't return here
      // The redirect will happen automatically
      await authService.signInWithGoogle();
    } catch (error) {
      // Show error message in the UI
      if (errorContainer) {
        errorContainer.textContent =
          error instanceof Error
            ? error.message
            : "Google Sign In failed. Please try again.";
        errorContainer.classList.remove("hidden");
      }

      // Reset button state
      if (googleSignInButton) {
        googleSignInButton.innerHTML =
          '<img src="/google.svg" alt="Google" class="w-5 h-5" /><span>Sign in with Google</span>';
        (googleSignInButton as HTMLButtonElement).disabled = false as boolean;
      }
    }
  });
</script>
