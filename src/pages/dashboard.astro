---
import { authService } from "../lib/auth";
import { TierManagementService } from "../lib/tierManagement";
import {
  authStore,
  getAuthenticatedUser as getStoreCurrentUser,
} from "../lib/authStore";
import type {
  UserSubscription,
  FeatureUsageEntry,
} from "../lib/tierManagement";
import { Timestamp } from "firebase/firestore";
import Layout from "../layouts/Layout.astro";
import Container from "../components/Container.astro";
import DashboardTools from "../components/DashboardTools.astro";
import QuoteDisplay from "../components/QuoteDisplay.astro";
import ResumeManager from "../components/tools/ResumeManager.astro";
import FeedbackModal from "../components/FeedbackModal.astro";
import SubscriptionExpiredModal from "../components/SubscriptionExpiredModal.astro";
import SearchBar from "../components/SearchBar.astro";
import ErrorModal from "../components/ErrorModal.astro";

// Server-side authentication and data fetching
export async function getStaticProps() {
  try {
    // Multiple strategies to get user
    const user =
      (await authService.getCurrentUser()) ||
      (await getStoreCurrentUser()) ||
      (await authStore.get()).user;

   if (!user) {
      return {
        redirect: {
          destination: "/login",
          permanent: false,
        },
      };
    }

    // Comprehensive name generation strategy
    const displayName =
      user.displayName || (user.email ? user.email.split("@")[0] : "User");

    const userData = {
      uid: user.uid,
      displayName,
      email: user.email,
    };

    const tierProfile = await TierManagementService.getUserTierProfile(
      user.uid
    );

    return {
      props: {
        userData,
        tierProfile,
      },
    };
  } catch (error) {
    console.error("Dashboard server-side error");

    return {
      props: {
        userData: {
          uid: "",
          displayName: "User",
          email: null,
        },
        tierProfile: {
          userId: "",
          currentTier: "free",
          featureUsage: {
            resumeGeneration: {
              usageCount: 0,
              maxAllowedUsage: 1,
            },
            coverLetterGeneration: {
              usageCount: 0,
              maxAllowedUsage: 1,
            },
          },
          subscriptionStartDate: Timestamp.now(),
          paymentStatus: "pending",
        },
      },
    };
  }
}

// Fetch user data and tier information
const {
  userData = {
    uid: "",
    displayName: "User",
    email: null,
  },
  tierProfile = {
    userId: "",
    currentTier: "free",
    featureUsage: {
      resumeGeneration: {
        usageCount: 0,
        maxAllowedUsage: 1,
      },
      coverLetterGeneration: {
        usageCount: 0,
        maxAllowedUsage: 1,
      },
    },
    subscriptionStartDate: Timestamp.now(),
    paymentStatus: "pending",
  },
} = Astro.props ?? {};

// Comprehensive display name generation
const displayName =
  userData.displayName ||
  (userData.email ? userData.email.split("@")[0] : "User");

// Calculate total feature usage
const calculateFeatureUsage = (
  tierProfile: UserSubscription | undefined,
  featureFilter: (feature: string) => boolean
): number => {
  if (!tierProfile || !tierProfile.featureUsage) return 0;

  return Object.entries(tierProfile.featureUsage)
    .filter(([feature]) => featureFilter(feature))
    .reduce(
      (sum, [, featureData]: [string, FeatureUsageEntry]) =>
        sum + featureData.usageCount,
      0
    );
};

const totalApplications = calculateFeatureUsage(tierProfile, (feature) =>
  feature.toLowerCase().includes("resume")
);

const totalInterviews = calculateFeatureUsage(tierProfile, (feature) =>
  feature.toLowerCase().includes("interview")
);

const totalSavedJobs = calculateFeatureUsage(tierProfile, (feature) =>
  feature.toLowerCase().includes("job")
);
---

<Layout
  title="Dashboard | Manage Your Career Tools"
  description="Access all your career tools in one place. Track your resume progress, manage job applications, and prepare for interviews."
  image="/images/dashboard-og-image.jpg"
  type="website"
  canonical="/dashboard"
>
  <SubscriptionExpiredModal />
  <!-- Error Modal Component -->
  <ErrorModal
    id="dashboardErrorModal"
    title="Error"
    message="An error occurred. Please try again."
    isOpen={false}
    zIndex={60}
  />
  <main class="relative min-h-screen dark:bg-gray-950 pt-16">
    <div class="relative">
      <section class="relative pt-20 pb-20">
        <Container>
          <div class="relative max-w-full">
            <div
              class="flex flex-col md:flex-row items-center justify-between gap-8"
            >
              <div class="text-center md:text-left md:w-1/2 space-y-6">
                <h1
                  class="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight"
                >
                  <span
                    class="block text-gray-950 text-3xl dark:text-white font-semibold"
                    >Welcome,</span
                  >
                  <span
                    id="userName"
                    class="text-gradient-brand-primary text-5xl dark:text-white font-semibold"
                    data-initial-name={displayName?.split(' ')[0] || 'User'}
                    >{displayName?.split(' ')[0] || 'User'}</span
                  >
                </h1>
             
              </div>
              <div class="md:w-1/2 relative">
                <QuoteDisplay />
              </div>
            </div>
          </div>
          <div
            aria-hidden="true"
            class="absolute inset-0 grid grid-cols-2 -space-x-52 opacity-30 dark:opacity-20"
          >
            <div
              class="blur-[106px] h-100 bg-gradient-to-br from-indigo-300 to-purple-500 dark:from-indigo-500 dark:to-purple-500"
            >
            </div>
            <div
              class="blur-[106px] h-100 bg-gradient-to-br from-cyan-500 to-purple-300 dark:from-cyan-500 dark:to-purple-500"
            >
            </div>
          </div>
        </Container>
      </section>

      <!-- Search Bar -->
      <section class="py-0 md:py-12">
        <SearchBar />
      </section>

      <!-- Tools Section -->
      <section class="py-12">
        <DashboardTools />
      </section>

      <!-- Resume Management Section -->
      <section class="py-12">
        <ResumeManager />
      </section>

      <!-- Feedback Section -->
      <section class="py-12">
        <Container>
          <div class="max-w-3xl mx-auto text-center">
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">We Value Your Feedback</h2>
            <p class="text-gray-600 dark:text-gray-300 mb-6">
              Your input helps us improve and provide a better experience for everyone.
            </p>
            <button
              id="openFeedbackButton"
              class="inline-flex items-center px-6 py-3 border border-transparent rounded-lg shadow-sm font-medium text-white bg-black dark:bg-white dark:text-black hover:bg-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary transition-colors duration-150"
            >
              Share Your Feedback
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </button>
            <FeedbackModal userId={userData.uid} userEmail={userData.email} />
          </div>
        </Container>
      </section>
    </div>
  </main>
</Layout>

<script type="module">
  import { authStore } from '../lib/authStore';

  document.addEventListener('DOMContentLoaded', () => {
    const userNameSpan = document.getElementById('userName');
    if (userNameSpan) {
      // Initial check from server-rendered data
      const initialName = userNameSpan.dataset.initialName;
      if (initialName && initialName !== 'User') {
        userNameSpan.textContent = initialName;
        userNameSpan.classList.remove('hidden');
      } else {
        // Fallback to client-side authStore
        const updateDisplayName = (store) => {
          const user = store.user;
          let displayValue = 'User';
          if (user && user.displayName) {
            displayValue = user.displayName.split(' ')[0];
          } else if (user && user.email) {
            displayValue = user.email.split('@')[0];
          }
          userNameSpan.textContent = displayValue;
          userNameSpan.classList.remove('hidden');
        };

        // Subscribe to authStore changes
        authStore.subscribe(updateDisplayName);

        // Also call once immediately with current state
        updateDisplayName(authStore.get());
      }
    }
  });
</script>

<style is:global>
  .text-gradient-brand-secondary {
    background-image: linear-gradient(to right, #6366f1, #8b5cf6, #d946e8);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
  }
</style>

<script src="../scripts/dashboardOptimistic.js"></script>

<script src="../scripts/interviewPrepOptimistic.js"></script>
<script src="../scripts/jobAnalysisOptimistic.js"></script>
<script src="../scripts/jobTrackerOptimistic.js"></script>
<script src="../scripts/linkedinOptimizerOptimistic.js"></script>
<script src="../scripts/coverLetterOptimistic.js"></script>
<script src="../scripts/guideModeOptimistic.js"></script>
<script src="../scripts/darkMode.ts"></script>

<script>
  document.addEventListener('DOMContentLoaded', () => {
    const openFeedbackButton = document.getElementById('openFeedbackButton');
    if (openFeedbackButton) {
      openFeedbackButton.addEventListener('click', () => {
        // Dispatch a custom event to open the feedback modal
        window.dispatchEvent(new CustomEvent('openFeedbackModalEvent'));
      });
    }
  });
</script>
