#!/bin/bash

# Make the script exit on any error
set -e

echo "🧹 Cleaning up Git repository based on .gitignore..."

# Remove .env files from Git tracking (but keep them on disk)
echo "🔒 Removing sensitive files from Git tracking..."
git rm --cached .env 2>/dev/null || true
git rm --cached google-credentials.json 2>/dev/null || true
git rm --cached secrets/* 2>/dev/null || true

# Remove build output from Git tracking
echo "🗑️  Removing build output from Git tracking..."
git rm -r --cached dist/ 2>/dev/null || true
git rm -r --cached .output/ 2>/dev/null || true
git rm -r --cached .astro/ 2>/dev/null || true

# Remove node_modules from Git tracking
echo "📦 Removing node_modules from Git tracking..."
git rm -r --cached node_modules/ 2>/dev/null || true

# Remove package lock files from Git tracking
echo "🔒 Removing lock files from Git tracking..."
git rm --cached package-lock.json 2>/dev/null || true
git rm --cached yarn.lock 2>/dev/null || true
git rm --cached pnpm-lock.yaml 2>/dev/null || true
git rm --cached bun.lock 2>/dev/null || true

# Remove Netlify folder from Git tracking
echo "🌐 Removing Netlify folder from Git tracking..."
git rm -r --cached .netlify/ 2>/dev/null || true

echo "✅ Cleanup complete!"
echo ""
echo "Now you should commit these changes with:"
echo "git add .gitignore"
echo "git commit -m \"Update .gitignore and remove tracked files that should be ignored\""
echo ""
echo "⚠️  Note: This script has removed files from Git tracking, but they still exist on your disk."
echo "   If these files contain sensitive information, consider rotating any secrets they contain."
