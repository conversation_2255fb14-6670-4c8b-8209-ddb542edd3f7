{"name": "astrolus", "type": "module", "version": "0.2.1", "private": true, "scripts": {"dev": "astro dev", "start": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "lint": "eslint .", "lint:fix": "eslint . --fix", "netlify": "netlify dev", "netlify:build": "netlify build"}, "dependencies": {"@astrojs/check": "^0.9.4", "@astrojs/netlify": "^6.4.1", "@astrojs/node": "^9.3.0", "@astrojs/react": "^4.3.0", "@astrojs/sitemap": "^3.4.1", "@google-cloud/talent": "^7.0.1", "@google/genai": "^1.5.0", "@google/generative-ai": "^0.24.1", "@iconify-json/mdi": "^1.2.3", "@mistralai/mistralai": "^1.7.2", "@nanostores/preact": "^1.0.0", "@netlify/emails": "^1.1.0", "@sendgrid/mail": "^8.1.5", "@types/cheerio": "^1.0.0", "@types/dompurify": "^3.0.5", "@types/uuid": "^10.0.0", "astro-font": "^1.1.0", "astro-icon": "^1.1.5", "axios": "^1.10.0", "bun": "^1.2.18", "cheerio": "^1.1.0", "chrome-aws-lambda": "^10.1.0", "crypto": "^1.0.1", "dompurify": "^3.2.6", "dotenv": "^17.0.0", "esbuild": "^0.25.6", "find-up": "^7.0.0", "firebase": "^11.9.1", "google-auth-library": "^10.1.0", "googleapis": "^152.0.0", "groq-sdk": "^0.26.0", "llamaapi": "^1.0.0", "locate-path": "^7.2.0", "lodash": "^4.17.21", "mammoth": "^1.9.1", "nanostores": "^1.0.1", "node-fetch": "^3.3.2", "nodemailer": "^7.0.5", "openai": "^5.8.2", "pdf-lib": "^1.17.1", "pdf-parse": "^1.1.1", "pdfjs-dist": "^5.3.31", "pdfreader": "^3.0.7", "puppeteer-core": "^24.12.1", "razorpay": "^2.9.5", "react": "^19.1.0", "react-dom": "^19.1.0", "resend": "^4.5.2", "tesseract.js": "^6.0.1", "textract": "^2.5.0", "uuid": "^11.0.5", "zod": "^3.25.64"}, "devDependencies": {"@astrojs/tailwind": "^6.0.0", "@eslint/js": "^9.28.0", "@iconify-json/lucide": "^1.2.57", "@iconify/tailwind": "^1.2.0", "@netlify/functions": "^4.1.5", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/node": "^24.0.1", "@types/nodemailer": "^6.4.17", "@types/pdf-parse": "^1.1.5", "@types/textract": "^2.4.5", "astro": "^5.9.3", "autoprefixer": "^10.4.21", "browserify-fs": "^1.0.0", "crypto-browserify": "^3.12.1", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-astro": "^1.3.1", "events": "^3.3.0", "firebase-admin": "^13.4.0", "firebase-functions": "^6.3.1", "netlify": "^22.2.2", "netlify-cli": "^22.2.2", "path-browserify": "^1.0.1", "postcss": "^8.5.5", "stream-browserify": "^3.0.0", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5.8.3", "typescript-eslint": "^8.34.0", "util": "^0.12.5", "vite-plugin-node-polyfills": "^0.24.0"}}